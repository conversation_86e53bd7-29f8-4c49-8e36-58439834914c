import os
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# Load environment variables from .env file
load_dotenv()

# --- Twilio Configuration ---
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_NUMBER = os.getenv("TWILIO_WHATSAPP_NUMBER")
RECIPIENT_NUMBER = os.getenv("RECIPIENT_WHATSAPP_NUMBER")

def send_whatsapp_reminder(customer_name, appointment_time, customer_phone):
    """
    Sends a WhatsApp reminder using Twilio.
    
    Args:
        customer_name (str): The name of the customer.
        appointment_time (str): The formatted time of the appointment.
        customer_phone (str): The customer's phone number in E.164 format.
    """
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_NUMBER]):
        print("Error: Twilio credentials are not fully configured in .env file.")
        return

    try:
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        # Construct the reminder message
        message_body = (
            f"Hi {customer_name}, this is a friendly reminder for your "
            f"appointment tomorrow at {appointment_time}. See you soon!"
        )

        # Send the message via WhatsApp
        # IMPORTANT: Twilio requires 'whatsapp:' prefix for both numbers.
        message = client.messages.create(
            from_=f'whatsapp:{TWILIO_NUMBER}',
            body=message_body,
            to=f'whatsapp:{customer_phone}'
        )
        
        print(f"Message sent successfully! SID: {message.sid}")

    except TwilioRestException as e:
        print(f"Error sending message: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    print("Sending a test WhatsApp reminder...")
    # --- Test Data ---
    # We use your own number from the .env file for this test.
    test_recipient_number = RECIPIENT_NUMBER
    
    if test_recipient_number:
         send_whatsapp_reminder(
            customer_name="Brian",
            appointment_time="10:30 AM",
            customer_phone=test_recipient_number
        )
    else:
        print("Please set RECIPIENT_WHATSAPP_NUMBER in your .env file to run a test.")