# supersaas_api.py

import datetime
from dotenv import load_dotenv
from SuperSaaS import Client
from SuperSaaS import Error

load_dotenv()

SCHEDULE_ID = 123456 # <--- MAKE SURE THIS IS YOUR SCHEDULE ID

def get_appointments_for_day(target_date):
    """
    Fetches all appointments from SuperSaaS for a specific date.

    Args:
        target_date (datetime.date): The date to fetch appointments for.

    Returns:
        list: A list of appointment objects, or an empty list if none are found or an error occurs.
    """
    client = Client.instance()
    
    start_of_day = datetime.datetime.combine(target_date, datetime.time.min)
    end_of_day = datetime.datetime.combine(target_date, datetime.time.max)

    print(f"Fetching appointments for {target_date.strftime('%Y-%m-%d')}...")

    try:
        # The API returns all appointments STARTING from the given time.
        # We fetch from the start of the day and will filter them manually.
        all_appointments = client.appointments.list(schedule_id=SCHEDULE_ID, start_time=start_of_day, limit=None)
        
        # Filter the list to include only appointments that start before the end of the target day.
        day_appointments = [
            appt for appt in all_appointments if appt.start < end_of_day
        ]
        
        return day_appointments

    except Error as e:
        print(f"An API error occurred: {e}")
        return [] # Return an empty list on error