import datetime
from dotenv import load_dotenv
from SuperSaaS import Client, Configuration
from SuperSaaS import Error

# Load variables from .env (SSS_API_ACCOUNT_NAME and SSS_API_KEY)
load_dotenv()

# --- Configuration ---
# You still need to set your Schedule ID here
SCHEDULE_ID = 123456 # <--- IMPORTANT: REPLACE WITH YOUR REAL SCHEDULE ID

# --- Main Script ---
def fetch_todays_appointments():
    """Connects to SuperSaaS and fetches appointments for the current day."""
    
    # 1. Initialize the SuperSaaS Client
    # The client automatically uses the SSS_... environment variables
    client = Client.instance()
    print("SuperSaaS client initialized.")

    # 2. Define the time range for today
    today_start = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    tomorrow_start = today_start + datetime.timedelta(days=1)
    
    print(f"Fetching appointments from {today_start.strftime('%Y-%m-%d %H:%M:%S')}...")

    # 3. Fetch the appointments from the API
    try:
        # CORRECTED: Using keyword arguments as shown in the documentation
        appointments = client.appointments.list(schedule_id=SCHEDULE_ID, start_time=today_start, limit=100)

        todays_appointments = [appt for appt in appointments if appt.start < tomorrow_start]

        if not todays_appointments:
            print("\nNo appointments found for today.")
        else:
            print(f"\nFound {len(todays_appointments)} appointment(s) for today:")
            for appt in todays_appointments:
                print(
                    f"  - ID: {appt.id}, "
                    f"Start: {appt.start.strftime('%I:%M %p')}, "
                    f"Customer: {appt.full_name}, "
                    f"Phone: {appt.phone}"
                )

    except Error as e:
        print(f"\nAn API error occurred: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_todays_appointments()