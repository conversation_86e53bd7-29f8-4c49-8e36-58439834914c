# twilio_api.py

import os
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

load_dotenv()

TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_NUMBER = os.getenv("TWILIO_WHATSAPP_NUMBER")

def send_whatsapp_reminder(customer_name, appointment_datetime, customer_phone):
    """Sends a WhatsApp reminder using Twilio."""
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_NUMBER]):
        print("Error: Twilio credentials are not fully configured.")
        return

    try:
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        appointment_time = appointment_datetime.strftime('%-I:%M %p') # e.g., "3:30 PM"

        message_body = (
            f"Hi {customer_name}, this is a reminder for your "
            f"appointment tomorrow at {appointment_time}. See you soon!"
        )

        message = client.messages.create(
            from_=f'whatsapp:{TWILIO_NUMBER}',
            body=message_body,
            to=f'whatsapp:{customer_phone}'
        )
        
        print(f"  - Successfully sent reminder to {customer_name} (SID: {message.sid})")

    except TwilioRestException as e:
        print(f"  - Error sending message to {customer_name}: {e}")